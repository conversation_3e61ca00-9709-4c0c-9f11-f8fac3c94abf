# TMS Domain - Система управления терминалами

## Обзор

Проект представляет собой комплексную систему для работы с терминалами, состоящую из:
- **Библиотечных модулей** для переиспользования логики
- **Микросервисов** для обработки и управления терминалами
- **API сервисов** для внешнего взаимодействия

Система построена на Kotlin/Spring Boot с автоматическим CI/CD pipeline для разработки, тестирования и развертывания.

## Архитектура

### Библиотечные модули
- `tms-common` - Общая логика и утилиты для работы с терминалами
- `tms-api` - Публичное API для работы с терминалами
- `tms-api-private` - Приватное API для внутренних сервисов
- `tms-model` - Модели данных и DTO

### Микросервисы
- `tms-gate` - API Gateway для внешних запросов
- `tms-processing` - Сервис обработки операций с терминалов
- `tms-console` - Консоль управления

## Структура проекта

```
tms-domain/
├── tms-common/                 # Общий модуль
├── tms-api/                    # Публичное API
├── tms-api-private/            # Приватное API
├── tms-model/                  # Модели данных
├── tms-gate/                   # API Gateway микросервис
├── tms-processing/             # Сервис обработки
├── tms-console/                # Консоль управления
├── charts/                     # Helm чарты для Kubernetes
│   ├── tms-gate/
│   ├── tms-processing/
│   └── tms-console/
├── scripts/                    # Скрипты для CI/CD
├── data/                       # Данные для локальной разработки
├── docker-compose.yml          # Локальное окружение
├── build.gradle.kts            # Корневая конфигурация Gradle
├── .gitlab-ci.yml              # GitLab CI/CD pipeline
├── Makefile                    # Команды для управления Docker
└── gradle.properties           # Версия проекта
```

## Технологический стек

- **Язык**: Kotlin
- **Фреймворк**: Spring Boot
- **База данных**: PostgreSQL
- **Очереди**: Apache Kafka
- **Аутентификация**: Keycloak
- **Координация**: Apache ZooKeeper
- **Контейнеризация**: Docker
- **Оркестрация**: Kubernetes + Helm
- **CI/CD**: GitLab CI/CD
- **Артефакты**: Nexus Repository

## Быстрый старт

### Предварительные требования

- Java 17
- Docker & Docker Compose
- Gradle 8.x (или используйте gradlew)
- Git

### Локальная разработка

1. **Клонирование репозитория**
```bash
git clone <repository-url>
cd tms-domain
```

2. **Сборка библиотечных модулей**
```bash
./gradlew build
```

3. **Запуск локального окружения**
```bash
# Запуск инфраструктуры (PostgreSQL, Keycloak, ZooKeeper)
docker-compose up -d tms_gate_db postgresql keycloak zoo1 zoo2 zoo3
```

4. **Сборка и запуск микросервисов**
```bash
# Сборка Docker образов
make docker-build

# Запуск микросервисов
make start
```

### Доступные сервисы

- **API Gateway**: http://localhost:5010
- **Keycloak**: http://localhost:8080
- **PostgreSQL**: localhost:5432
- **ZooKeeper**: localhost:2181, 2182, 2183

## CI/CD Pipeline

Проект использует GitLab CI/CD с Docker executor для автоматизации:

### Сценарии pipeline:

1. **Любая ветка (кроме develop/master)**
   - Сборка и тестирование библиотечных модулей

2. **Ветка develop**
   - Сборка и тестирование
   - Публикация SNAPSHOT версий в Nexus
   - Сборка и деплой микросервисов в тестовое окружение

3. **Ветка master / Теги**
   - Выпуск релизных версий
   - Публикация в Nexus
   - Сборка и деплой микросервисов в продакшн

### Особенности:
- Использует Docker executor с тегом `docker`
- Автоматическое создание `gradle.properties` из переменных окружения
- Кэширование Gradle для ускорения сборок
- Helm чарты для Kubernetes деплоя
- Валидация Kubernetes манифестов с помощью kubeval

## Разработка

### Структура веток
- `master` - основная ветка для релизов
- `develop` - ветка для разработки и тестирования
- `feature/*` - ветки для новых функций
- `release/*` - ветки для подготовки релизов
- `hotfix/*` - ветки для критических исправлений

### Процесс разработки
1. Создать feature ветку от `develop`
2. Разработать функциональность
3. Создать MR в `develop`
4. После тестирования создать MR из `develop` в `master`

### Полезные команды

```bash
# Сборка проекта
./gradlew build

# Запуск тестов
./gradlew test

# Публикация SNAPSHOT (локально)
./gradlew publishToNexusSnapshot

# Публикация релиза (локально)
./gradlew publishToNexus

# Выпуск релиза
./gradlew release

# Docker команды
make docker-build          # Сборка образов
make start                 # Запуск сервисов
make stop                  # Остановка сервисов
make restart               # Перезапуск сервисов
make rebuild-and-restart   # Пересборка и перезапуск
```

## Конфигурация

### Переменные окружения для микросервисов

**tms-gate:**
- `DB_URL` - URL PostgreSQL базы данных
- `DB_USER` - Пользователь базы данных
- `DB_PASSWORD` - Пароль базы данных
- `DB_MIGRATION_ENABLE` - Включение миграций
- `MAVEN_USER` - Пользователь Maven репозитория
- `MAVEN_PASSWORD` - Пароль Maven репозитория
- `ZOOKEEPER_NODES` - Адреса ZooKeeper узлов

**tms-processing:**
- `MAVEN_USER` - Пользователь Maven репозитория
- `MAVEN_PASSWORD` - Пароль Maven репозитория
- `ZOOKEEPER_NODES` - Адреса ZooKeeper узлов

### Настройка CI/CD

Для работы pipeline необходимо настроить переменные в GitLab:

1. Перейти в **Settings** → **CI/CD** → **Variables**
2. Добавить переменные:
   - `MAVEN_USER` (Protected)
   - `MAVEN_PASSWORD` (Protected + Masked)
   - `DOCKER_REPOSITORY_ADDR` - Адрес Docker registry
   - `KUBECONFIG_DEVELOP` - Kubeconfig для тестового кластера

## Публикация в Nexus

Проект настроен для публикации в Nexus репозиторий:

- **SNAPSHOT**: `https://nexus.sbertroika.tech/repository/maven-snapshots/`
- **Releases**: `https://nexus.sbertroika.tech/repository/maven-releases/`

### Артефакты:
- `ru.sbertroika.tms:tms-common:${version}`
- `ru.sbertroika.tms:tms-api:${version}`
- `ru.sbertroika.tms:tms-api-private:${version}`
- `ru.sbertroika.tms:tms-model:${version}`

## Развертывание

### Kubernetes

Микросервисы развертываются в Kubernetes с помощью Helm чартов:

```bash
# Деплой в тестовое окружение
helm upgrade tms-gate charts/tms-gate \
  --install \
  --set namespace=tms \
  --set image.tag=develop

# Деплой в продакшн
helm upgrade tms-gate charts/tms-gate \
  --install \
  --set namespace=tms \
  --set image.tag=v1.0.0
```

### Docker

Для локального развертывания используйте docker-compose или Makefile:

```bash
# Полное окружение
make docker-build-and-run

# Только инфраструктура
docker-compose up -d tms_gate_db postgresql keycloak zoo1 zoo2 zoo3
```

## Troubleshooting

### Ошибки сборки
- Убедиться, что используется Java 17
- Проверить, что все зависимости доступны в Nexus
- Проверить, что gradle.properties содержит корректные учетные данные

### Ошибки CI/CD
- Проверить настройки переменных окружения в GitLab
- Убедиться, что GitLab runner с тегом `docker` доступен

### Ошибки локального запуска
- Проверить, что Docker и Docker Compose установлены
- Убедиться, что порты не заняты другими сервисами
- Проверить логи контейнеров: `docker-compose logs <service-name>`

### Ошибки публикации
- Проверить учетные данные Nexus
- Убедиться, что версия корректна и не конфликтует

## Релиз

### Подготовка к релизу

Проверить:
- Не должно быть uncommitted данных
- Данные должны соответствовать репозиторию (git push)
- Корректно указанная версия в gradle.properties
- Если релиз нужно сделать повторно, например был релиз версии 1.0.0 и нужно заново сделать релиз на версию 1.0.0, то необходимо очистить информацию в тегах (из корня проекта):
  ```bash
  git tag -d v1.0.0-SNAPSHOT
  git push origin :refs/tags/v1.0.0-SNAPSHOT
  ```

### Выполнение релиза

```bash
# Из корня проекта (tms-domain)
./gradlew release
```

## Контакты

Для вопросов по проекту обращайтесь к команде разработки.
