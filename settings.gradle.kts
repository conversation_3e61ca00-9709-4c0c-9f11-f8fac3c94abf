rootProject.name = "cbt-domain"

include(
    ":troika-api",
    ":troika-askp-connector",
    ":troika-askp-connector-api",
    ":troika-emission",
    ":troika-settings-model",
    ":troika-gateway",
    ":troika-model",
    ":troika-processing",
    ":troika-emission-api"
   )

dependencyResolutionManagement {
    repositories {
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }

    versionCatalogs {
        create("libs") {
            from("ru.sbertroika.sharedcatalog:gradle-common:1.3")
        }
    }
}