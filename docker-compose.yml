version: '3.7'

services:
  troika_gateway:
    image: troika-gateway:local
    container_name: troika-gateway
    ports:
      - 5005:5000
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      DB_URL: postgresql://cbt-db:5432/cbt
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_MIGRATION_ENABLE: "true"
      R2DB_URL: r2dbc:******************************************/cbt
      ZOOKEEPER_NODES: zoo1:2181
      KAFKA_SERVERS: kafka:29092
    depends_on:
      - cbt_db
      - kafka
    restart: always
    networks:
      - cbt_network

  cbt_db:
    image: postgres:14
    container_name: cbt-db
    restart: always
    ports:
      - 5432:5432
    volumes:
      - ./data/cbt:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: cbt
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - cbt_network

  troika_askp_connector:
    image: troika-askp-connector:local
    container_name: troika-askp-connector
    ports:
      - 5010:5000
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    restart: always
    networks:
      - cbt_network

  troika_processing:
    image: troika-processing:local
    container_name: troika-processing
    ports:
      - 5011:5000
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - cbt_db
      - kafka
    restart: always
    environment:
      DB_URL: postgresql://cbt-db:5432/cbt
      DB_USER: postgres
      DB_PASSWORD: postgres
      R2DB_URL: ******************************************/cbt
      ZOOKEEPER_NODES: zoo1:2181
      S3_URL: http://s3:9001
      S3_ACCESS_KEY_ID: s3__user
      S3_SECRET_ACCESS_KEY: s3__pass
      S3_BUCKET: tkp3-manifest
      KAFKA_SERVERS: kafka:29092
    networks:
      - cbt_network

  troika_emission:
    image: troika-emission:local
    container_name: troika-emission
    ports:
      - 5007:5000
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      - DB_URL=******************************************************/troika
      - FLYWAY_DB_URL=******************************************************/troika
      - DB_USER=postgres
      - DB_PASSWORD=postgres
    depends_on:
      - troika_emission_db
    restart: always
    networks:
      - cbt_network

  troika_emission_db:
    image: postgres:14
    container_name: troika_emission_db
    restart: always
    ports:
      - 5436:5432
    volumes:
      - ./data/emission:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: troika
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - cbt_network

  keycloak_db:
    image: docker.io/bitnami/postgresql:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - POSTGRESQL_USERNAME=bn_keycloak
      - POSTGRESQL_DATABASE=bitnami_keycloak
    networks:
      - keycloak_net

  keycloak:
    image: docker.io/bitnami/keycloak:latest
    container_name: keycloak
    ports:
      - "8080:8080"
    environment:
      - KEYCLOAK_CREATE_ADMIN_USER=true
      - KEYCLOAK_ADMIN_USER=user
      - KEYCLOAK_ADMIN_PASSWORD=12345
    depends_on:
      - keycloak_db
    networks:
      - keycloak_net

  zoo1:
    image: zookeeper
    restart: always
    hostname: zoo1
    ports:
      - "2181:2181"
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181 server.2=zoo2:2888:3888;2181 server.3=zoo3:2888:3888;2181
    networks:
      - cbt_network

  zoo2:
    image: zookeeper
    restart: always
    hostname: zoo2
    ports:
      - "2182:2181"
    environment:
      ZOO_MY_ID: 2
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181 server.2=zoo2:2888:3888;2181 server.3=zoo3:2888:3888;2181
    networks:
      - cbt_network

  zoo3:
    image: zookeeper
    restart: always
    hostname: zoo3
    ports:
      - "2183:2181"
    environment:
      ZOO_MY_ID: 3
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181 server.2=zoo2:2888:3888;2181 server.3=zoo3:2888:3888;2181
    networks:
      - cbt_network

  s3:
    image: minio/minio:RELEASE.2025-05-24T17-08-30Z
    container_name: s3
    environment:
      MINIO_ROOT_USER: s3__user
      MINIO_ROOT_PASSWORD: s3__pass
      MINIO_DOMAIN: s3 # переключение в режим virtual-hosts-style
    ports:
      - 9000:9000 # S3 API
      - 9001:9001 # WebUI
    volumes:
      - ./data/minio:/data
    command: server --console-address ":9001" /data
    networks:
      - cbt_network

  kafka:
    image: confluentinc/cp-kafka:7.5.9
    container_name: kafka
    depends_on:
      - zoo1
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zoo1:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    networks:
      - cbt_network

  kafka-ui:
    image: provectuslabs/kafka-ui:v0.7.2
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - 8086:8080
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zoo1:2181
    networks:
      - cbt_network

networks:
  cbt_network:
    driver: bridge
  keycloak_net:
    driver: bridge