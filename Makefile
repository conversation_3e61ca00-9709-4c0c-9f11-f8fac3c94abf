docker-build-and-run: docker-build-gateway docker-build-askp-connector docker-build-processing docker-build-emission
	docker-compose up -d

docker-run-infra:
	docker-compose up -d troika-emission_db keycloak_db keycloak zoo1 s3 kafka kafka-ui

docker-build: docker-build-gateway docker-build-askp-connector docker-build-processing docker-build-emission

start:
	docker-compose up -d

restart:
	docker-compose stop
	docker-compose rm -vf troika_gateway
	docker-compose rm -vf troika_processing
	docker-compose rm -vf troika_askp_connector
	docker-compose rm -vf troika_emission
	docker-compose up -d

restart-all:
	docker-compose stop && docker-compose rm -vf && docker-compose up -d

docker-rm:
	docker-compose stop && docker-compose rm -vf

docker-rm-all:
	docker-compose rm -vf

stop:
	docker-compose stop

rebuild-and-restart: stop docker-rm docker-build start

rebuild-and-restart-all: stop docker-rm-all docker-build start

docker-build-gateway:
	@echo "[INFO] Сборка docker-образа troika-gateway с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t troika-gateway:local \
		-f troika-gateway/Dockerfile .
	rm -rf .ci-gradle

docker-build-askp-connector:
	@echo "[INFO] Сборка docker-образа troika-askp-connector с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t troika-askp-connector:local \
		-f troika-askp-connector/Dockerfile .
	rm -rf .ci-gradle

docker-build-processing:
	@echo "[INFO] Сборка docker-образа troika-processing с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t troika-processing:local \
		-f troika-processing/Dockerfile .
	rm -rf .ci-gradle

docker-build-emission:
	@echo "[INFO] Сборка docker-образа troika-emission с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t troika-emission:local \
		-f troika-emission/Dockerfile .
	rm -rf .ci-gradle