create table st_loader_credentials
(
    id                 uuid      default gen_random_uuid() not null,
    version            int       default 1                 not null,
    PRIMARY KEY (id, version),
    version_created_at TIMESTAMP default now()             not null,
    version_created_by uuid,
    project_id         uuid,
    tpp_type           varchar(100)                        not null,
    terminal_serial    varchar(150)                        not null,
    tid                varchar(150)                        not null,
    is_active          boolean   default false,
    tags               TEXT
);

CREATE TABLE troika_credentials
(
    id                 uuid      default gen_random_uuid() not null,
    version            int       default 1                 not null,
    PRIMARY KEY (id, version),
    version_created_at TIMESTAMP default now()             not null,
    version_created_by uuid,
    project_id         uuid,
    org_id             uuid,
    terminal_id        uuid,
    tid                varchar(150)                        not null,
    driver_number      integer                             not null,
    exit_number        integer                             not null,
    park_number        integer                             not null,
    route_number       integer                             not null,
    merchant           varchar(150),
    is_active          boolean   default false,
    tags               TEXT
);