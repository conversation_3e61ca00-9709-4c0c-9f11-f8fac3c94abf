package ru.sbertroika.tkp3.troika.settings.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.ZonedDateTime
import java.util.*

@Table("st_loader_credentials")
data class SlLoaderCredentials(

    @Column("id")
    var id: UUID? = null,

    @Column("version")
    var version: Int? = null,

    @Column("version_created_at")
    var versionCreatedAt: ZonedDateTime? = null,

    @Column("version_created_by")
    var versionCreatedBy: UUID? = null,

    /**
     * Идентификатор проекта
     */
    @Column("project_id")
    var projectId: UUID? = null,

    /**
     * Заводской номер терминала
     */
    @Column("terminal_serial")
    var terminalSerial: String? = null,

    /**
     * Идентификатор технического терминала
     */
    @Column("tid")
    var tid: String? = null,

    @Column("is_active")
    var isActive: Boolean? = null,

    /**
     * Тэги
     */
    @Column("tags")
    var tags: String? = null
)
