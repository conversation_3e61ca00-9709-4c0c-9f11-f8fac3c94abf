package ru.sbertroika.tkp3.troika.settings.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

data class TroikaCredentialsPK(
    val id: UUID? = null,
    val version: Int? = null,
) : Serializable

@Table("troika_credentials")
data class TroikaCredentials(

    @Column("id")
    var id: UUID? = null,

    @Column("version")
    var version: Int? = null,

    @Column("version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("version_created_by")
    var versionCreatedBy: UUID? = null,

    /**
     * Идентификатор проекта
     */
    @Column("project_id")
    var projectId: UUID? = null,

    /**
     * Идентификатор организации
     */
    @Column("org_id")
    var orgId: UUID? = null,

    /**
     * Идентификатор терминала
     */
    @Column("terminal_id")
    var terminalId: UUID? = null,

    /**
     * Внешний идентификатор терминала
     */
    @Column("tid")
    var tid: String? = null,

    /**
     * Номер водителя
     */
    @Column("driver_number")
    var driverNumber: Int? = null,

    /**
     * Номер выхода
     */
    @Column("exit_number")
    var exitNumber: Int? = null,

    /**
     * Код парка
     */
    @Column("park_number")
    var parkNumber: Int? = null,

    /**
     * Номер маршрута
     */
    @Column("route_number")
    var routeNumber: Int? = null,

    /**
     * Мерчант
     */
    @Column("merchant")
    var merchant: String? = null,

    @Column("is_active")
    var isActive: Boolean? = null,

    /**
     * Тэги
     */
    @Column("tags")
    var tags: String? = null
)
