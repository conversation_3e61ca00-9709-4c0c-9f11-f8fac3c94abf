#!/bin/bash
# Helm validation for all charts in charts/ folder

set -e

# Default stage
STAGE="dev"

# Prompt for namespace
read -p "Enter namespace for validation: " NAMESPACE

if [ -z "$NAMESPACE" ]; then
    echo "Error: Namespace cannot be empty"
    exit 1
fi

echo "Validating charts in namespace: $NAMESPACE (stage: $STAGE)"
echo "=========================================="

# Counter for validation results
TOTAL_CHARTS=0
SUCCESSFUL_CHARTS=0
FAILED_CHARTS=0

# Iterate through all chart directories
for CHART_DIR in ./*/ ; do
    # Skip if not a directory
    if [ ! -d "$CHART_DIR" ]; then
        continue
    fi
    
    CHART_NAME=$(basename "$CHART_DIR")
    
    # Skip the current directory (charts/)
    if [ "$CHART_NAME" = "charts" ]; then
        continue
    fi
    
    # Check if required files exist
    if [[ -f "$CHART_DIR/values.yaml" && -f "$CHART_DIR/values.$STAGE.yaml" ]]; then
        echo "Validating $CHART_NAME..."
        TOTAL_CHARTS=$((TOTAL_CHARTS + 1))
        
        # Run helm validation
        if helm install --dry-run -n "$NAMESPACE" "$CHART_NAME" "$CHART_DIR" \
           -f "$CHART_DIR/values.yaml" \
           -f "$CHART_DIR/values.$STAGE.yaml" > /dev/null 2>&1; then
            echo "✓ $CHART_NAME validation successful"
            SUCCESSFUL_CHARTS=$((SUCCESSFUL_CHARTS + 1))
        else
            echo "✗ $CHART_NAME validation failed"
            echo "  Running detailed validation for debugging:"
            helm install --dry-run -n "$NAMESPACE" "$CHART_NAME" "$CHART_DIR" \
                -f "$CHART_DIR/values.yaml" \
                -f "$CHART_DIR/values.$STAGE.yaml"
            FAILED_CHARTS=$((FAILED_CHARTS + 1))
        fi
        echo ""
    else
        echo "Skipping $CHART_NAME: missing values.yaml or values.$STAGE.yaml"
        echo ""
    fi
done

# Summary
echo "=========================================="
echo "Validation Summary:"
echo "Total charts processed: $TOTAL_CHARTS"
echo "Successful validations: $SUCCESSFUL_CHARTS"
echo "Failed validations: $FAILED_CHARTS"

if [ $FAILED_CHARTS -eq 0 ]; then
    echo "✓ All charts validated successfully!"
    exit 0
else
    echo "✗ Some charts failed validation"
    exit 1
fi
