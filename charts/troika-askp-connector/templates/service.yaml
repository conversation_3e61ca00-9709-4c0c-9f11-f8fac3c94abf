apiVersion: v1
kind: Service
metadata:
  name: {{ include "troika-askp-connector.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "troika-askp-connector.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - name: grpc
      port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
    - name: grpc2
      port: {{ .Values.service.grpc2.port }}
      targetPort: {{ .Values.service.grpc2.targetPort }}
      protocol: TCP
  selector:
    {{- include "troika-askp-connector.selectorLabels" . | nindent 4 }}
