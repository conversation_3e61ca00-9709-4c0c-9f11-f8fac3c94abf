syntax = "proto3";

package ru.sbertroika.troika.askp.connector;

import "google/protobuf/timestamp.proto";
import "common.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.troika.askp.connector";

service TroikaASKPConnector {
  rpc sendBatch(BatchRequest) returns (BatchResponse);
}

enum PassType {
  PT_WALLET = 0;
  PT_TAT = 1;
}

message Pass {
  string trxId = 1;
  PassType type = 2;
  google.protobuf.Timestamp createdAt = 3;
  uint32 routeNumber = 4;
  uint32 driverNumber = 5;
  uint32 shiftNumber = 6;
  uint32 park = 7;
  string uid = 8;
  string data = 9;
  uint32 amount = 10;
}

message BatchRequest {
  string terminalId = 1;
  repeated Pass pass = 2;
}

message BatchResponse {
  oneof response {
    common.v1.OperationError error = 1;
    string sessionId = 2;
  }
}
