<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="openjdk-17" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/api" />
            <option value="$PROJECT_DIR$/common-api" />
            <option value="$PROJECT_DIR$/model" />
            <option value="$PROJECT_DIR$/troika-askp-connector-api" />
            <option value="$PROJECT_DIR$/troika-emission-api" />
            <option value="$PROJECT_DIR$/troika-settings-model" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>