package ru.sbertroika.tkp3.troika.processing.output.repository

import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.troika.settings.model.TroikaCredentials
import ru.sbertroika.tkp3.troika.settings.model.TroikaCredentialsPK
import java.util.*

interface TroikaCredentialsRepository : CoroutineCrudRepository<TroikaCredentials, TroikaCredentialsPK> {

    suspend fun findAllByProjectIdOrOrgIdOrTerminalId(projectId: UUID, orgId: UUID, terminalId: UUID): List<TroikaCredentials>
}