package ru.sbertroika.tkp3.troika.processing.service

import arrow.core.Either
import arrow.core.computations.ResultEffect.bind
import arrow.core.left
import arrow.core.right
import io.grpc.ManagedChannelBuilder
import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.common.ServiceError
import ru.sbertroika.common.toTimestamp
import ru.sbertroika.common.v1.ErrorType
import ru.sbertroika.tkp3.troika.api.model.TroikaTap
import ru.sbertroika.tkp3.troika.api.model.TroikaTicketType
import ru.sbertroika.tkp3.troika.model.*
import ru.sbertroika.tkp3.troika.processing.model.ErrorOperation
import ru.sbertroika.tkp3.troika.processing.model.Troika
import ru.sbertroika.tkp3.troika.processing.util.mapper
import ru.sbertroika.tkp3.troika.settings.model.TroikaCredentials
import ru.sbertroika.troika.askp.connector.*
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

@Service
class ASKPProcessingService(
    kafkaProducerFactory: ProducerFactory<String, Any>,
    private val emissionService: EmissionService,

    @Value("\${service.askp_connector_url}")
    private val askpConnectorUrl: String,
    @Value("\${spring.kafka.troika_trx_in_topic}")
    private val trxTopic: String,
    @Value("\${spring.kafka.troika_operation_out_topic}")
    private val operationTopic: String,
    @Value("\${spring.kafka.troika_error_out_topic}")
    private val errorTopic: String
) : ProcessingService {

    private val mapper = mapper()
    private val producer = kafkaProducerFactory.createProducer()
    private val channel = ManagedChannelBuilder.forTarget(askpConnectorUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()

    private val client = TroikaASKPConnectorGrpcKt.TroikaASKPConnectorCoroutineStub(channel)

    override suspend fun process(messages: List<Troika>): Either<Throwable, Unit> = Either.catch {
        messages
            .mapNotNull { message ->
                if (message.cred == null) {
                    pushErrorOperation(
                        ErrorOperation(
                            trxId = message.tap.trxId.toString(),
                            error = "credentials not found"
                        )
                    )
                    null
                } else {
                    message
                }
            }
            .groupByTo(mutableMapOf()) { "${it.tap.terminalId}" }
            .forEach { group ->
                val passList = mutableListOf<Pass>()
                group.value.map { g ->
                    val cardId = emissionService.emission(g.tap.cardUid).bind()
                    pushTrx(toTroikaTrx(g.tap, g.cred!!, cardId))
                    passList.add(
                        pass {
                            trxId = g.tap.trxId.toString()
                            type = when (g.tap.ticketType) {
                                TroikaTicketType.WALLET -> PassType.PT_WALLET
                                TroikaTicketType.TAT -> PassType.PT_TAT
                            }
                            createdAt = g.tap.createdAt.toTimestamp()
                            routeNumber = g.cred.routeNumber!!
                            driverNumber = g.cred.driverNumber!!
                            park = g.cred.parkNumber!!
                            shiftNumber = g.tap.shiftNum
                            amount = g.tap.amount
                            uid = g.tap.cardUid
                            data = g.tap.raw
                        }
                    )
                }

                try {
                    val response = client.sendBatch(
                        batchRequest {
                            terminalId = group.value.first().cred!!.tid!!
                            pass += passList
                        }
                    )

                    if (response.hasError()) {
                        //TODO отправить в очередь на повтор
                        if (response.error.type == ErrorType.SERVICE_ERROR) throw ServiceError(response.error.message)
                        group.value.forEach { op ->
                            pushOperation(
                                TroikaOperation(
                                    trxId = op.tap.trxId,
                                    recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
                                    type = OperationType.SEND,
                                    status = OperationStatus.FAIL,
                                    errorCode = if (response.error.code > 0) response.error.code else null,
                                    errorMessage = if (response.error.message.isNullOrEmpty()) null else response.error.message
                                )
                            )
                        }
                    } else {
                        group.value.forEach { op ->
                            pushOperation(
                                TroikaOperation(
                                    trxId = op.tap.trxId,
                                    recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
                                    type = OperationType.SEND,
                                    status = OperationStatus.SUCCESS,
                                    batchId = response.sessionId,
                                    park = "${op.cred?.parkNumber}",
                                    route = "${op.cred?.routeNumber}",
                                    tid = op.cred?.tid,
                                    ern = op.tap.ern
                                )
                            )
                        }
                    }

                    Unit.right()
                } catch (e: Exception) {
                    Error(e).left()
                }
            }

        Unit.right()
    }

    private fun toTroikaTrx(tap: TroikaTap, cred: TroikaCredentials, cardId: String): TroikaTrx = TroikaTrx(
        trxId = tap.trxId,
        createdAt = tap.createdAt,
        recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
        ticketType = when(tap.ticketType) {
            TroikaTicketType.WALLET -> TicketType.WALLET
            TroikaTicketType.TAT -> TicketType.TICKET
        },
        crdId = UUID.fromString(cardId),
        terminalSerial = tap.terminalSerial,
        tid = cred.tid,
        shiftNum = tap.shiftNum.toUInt(),
        ern = tap.ern.toUInt(),
        amount = tap.amount.toUInt(),
        uid = tap.cardUid,
        tags = "troika,${getType(tap.ticketType)}"
    )

    private fun getType(ticketType: TroikaTicketType): String = when (ticketType) {
        TroikaTicketType.WALLET -> "wallet"
        TroikaTicketType.TAT -> "ab"
    }

    private fun pushOperation(operation: TroikaOperation) = Either.catch {
        val out = ProducerRecord<String, Any>(operationTopic, operation.trxId.toString(), mapper.writeValueAsString(operation))
        producer.send(out)
    }

    private fun pushErrorOperation(operation: ErrorOperation) = Either.catch {
        val out = ProducerRecord<String, Any>(errorTopic, operation.trxId, mapper.writeValueAsString(operation))
        producer.send(out)
    }

    private fun pushTrx(trx: TroikaTrx) = Either.catch {
        val out = ProducerRecord<String, Any>(trxTopic, trx.trxId.toString(), mapper.writeValueAsString(trx))
        producer.send(out)
    }
}