package ru.sbertroika.tkp3.troika.processing.input

import arrow.core.Either
import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.producer.ProducerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.core.ProducerFactory
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.troika.api.model.TroikaTap
import ru.sbertroika.tkp3.troika.processing.model.ErrorOperation
import ru.sbertroika.tkp3.troika.processing.service.ProcessingService
import ru.sbertroika.tkp3.troika.processing.service.SettingsService
import ru.sbertroika.tkp3.troika.processing.util.mapper

@Profile("batch")
@Component
class TroikaBatchProcessingConsumer(
    kafkaProducerFactory: ProducerFactory<String, Any>,
    val processingService: ProcessingService,

    private val settingsService: SettingsService,

    @Value("\${spring.kafka.troika_error_out_topic}")
    private val errorTopic: String
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()

    @KafkaListener(groupId = "\${spring.kafka.troika_batch_processing_in_group}", topics = ["\${spring.kafka.troika_in_topic}"])
    fun receive(records: List<ConsumerRecord<String, String>>, acknowledgment: Acknowledgment) = runBlocking {
        try {
            //TODO сделать разделение на очереди через rabbitmq
            val operations = settingsService.makeConfig(
                records.mapNotNull { record ->
                    toOperationMessage(record.value()).fold(
                        {
                            logger.error("error process transaction ${record.key()}: ${it.message}")
                            val out = ProducerRecord<String, Any>(errorTopic, record.key(), mapper.writeValueAsString(it.message))
                            producer.send(out)
                            null
                        },
                        { it }
                    )
                }.toList()
            ).fold(
                {
                    logger.error("error process transaction", it)
                    return@runBlocking
                },
                { it }
            )

            processingService.process(operations).fold(
                { err ->
                    //TODO Забросить в очередь на повторы а не откладывать задание
                    for (rec in operations) {
                        val out = ProducerRecord<String, Any>(
                            errorTopic,
                            rec.tap.trxId.toString(),
                            mapper.writeValueAsString(
                                ErrorOperation(
                                    trxId = rec.tap.trxId.toString(),
                                    error = err.message
                                )
                            )
                        )
                        producer.send(out)
                    }
                    Thread.sleep(3000)
                },
                {
                    acknowledgment.acknowledge()
                }
            )
        } catch (e: Exception) {
            logger.error("error process transaction ${records.map { it.key() }.toList()}", e)
            acknowledgment.acknowledge()
        }
    }

    private fun toOperationMessage(data: String): Either<Throwable, TroikaTap> = Either.catch {
        mapper.readValue<TroikaTap>(data)
    }
}