package ru.sbertroika.tkp3.troika.processing.service

import arrow.core.Either
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.troika.api.model.TroikaTap
import ru.sbertroika.tkp3.troika.processing.model.Troika
import ru.sbertroika.tkp3.troika.processing.output.repository.TroikaCredentialsRepository
import ru.sbertroika.tkp3.troika.settings.model.TroikaCredentials

@Service
class SettingsServiceImpl(
    private val troikaCredentialsRepository: TroikaCredentialsRepository
) : SettingsService {

    override suspend fun makeConfig(list: List<TroikaTap>): Either<Throwable, List<Troika>> = Either.catch {
        val configs = mutableMapOf<String, TroikaCredentials>()
        list.forEach { tap ->
            val key = "${tap.projectId}-${tap.orgId}-${tap.terminalId}"
            if (!configs.containsKey(key)) {
                val creds = troikaCredentialsRepository.findAllByProjectIdOrOrgIdOrTerminalId(tap.projectId, tap.orgId, tap.terminalId)
                if (creds.isNotEmpty()) {
                    val byTerm = creds.find { it.terminalId == tap.terminalId }
                    if (byTerm != null) {
                        configs[key] = byTerm
                    } else {
                        val byOrg = creds.find { it.orgId == tap.orgId }
                        if (byOrg != null) {
                            configs[key] = byOrg
                        } else {
                            configs[key] = creds.first()
                        }
                    }
                }
            }
        }

        list.mapNotNull { tap ->
            val key = "${tap.projectId}-${tap.orgId}-${tap.terminalId}"
            if (configs.containsKey(key)) {
                val config = configs[key]!!
                Troika(
                    cred = config,
                    tap = tap
                )
            } else {
                Troika(
                    tap = tap
                )
            }
        }.toList()
    }
}