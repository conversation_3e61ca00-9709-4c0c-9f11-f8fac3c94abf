package ru.sbertroika.tkp3.troika.processing.config

import io.r2dbc.spi.ConnectionFactories
import io.r2dbc.spi.ConnectionFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.r2dbc.core.DefaultReactiveDataAccessStrategy
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.data.r2dbc.dialect.PostgresDialect
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories
import org.springframework.r2dbc.core.DatabaseClient

@Configuration
@EnableR2dbcRepositories(
    basePackages = ["ru.sbertroika.tkp3.troika.processing.output.repository"],
    entityOperationsRef = "troikaR2dbcEntityOperations"
)
class TroikaDataSourceConfiguration(
    @Value("\${spring.r2dbc.url}")
    private val url: String
) {

    @Bean
    @Primary
    @Qualifier("troikaConnectionFactory")
    fun troikaConnectionFactory(): ConnectionFactory = ConnectionFactories.get(url)

    @Bean
    @Primary
    @Qualifier("troikaR2dbcEntityOperations")
    fun troikaR2dbcEntityOperations(@Qualifier("troikaConnectionFactory") connectionFactory: ConnectionFactory): R2dbcEntityOperations {
        val strategy = DefaultReactiveDataAccessStrategy(PostgresDialect.INSTANCE)
        val databaseClient = DatabaseClient.builder()
            .connectionFactory(connectionFactory)
            .build()
        return R2dbcEntityTemplate(databaseClient, strategy)
    }

    @Bean
    @Primary
    @Qualifier("troikaDatabaseClient")
    fun troikaDatabaseClient(@Qualifier("troikaConnectionFactory") connectionFactory: ConnectionFactory): DatabaseClient {
        return DatabaseClient.builder()
            .connectionFactory(connectionFactory)
            .bindMarkers(PostgresDialect.INSTANCE.bindMarkersFactory)
            .namedParameters(true)
            .build()
    }
}