package ru.sbertroika.tkp3.troika.processing.service

import arrow.core.Either
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.troika.emission.v1.EmissionRequest
import ru.sbertroika.troika.emission.v1.TroikaEmissionServiceGrpcKt

@Service
class EmissionServiceImpl(
    @Value("\${service.troika_emission_url}")
    private val emissionServiceUrl: String
) : EmissionService {

    private final val channel = ManagedChannelBuilder.forTarget(emissionServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    val client = TroikaEmissionServiceGrpcKt.TroikaEmissionServiceCoroutineStub(channel)

    override suspend fun emission(uid: String): Either<Throwable, String> = Either.catch {
        val request = EmissionRequest.newBuilder()
            .setUid(uid)
            .build()
        val response = client.emission(request)

        if (response.hasError()) throw Error(response.error.message)

        response.cardId
    }
}