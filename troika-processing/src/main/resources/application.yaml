spring:
  application:
    name: troika-processing

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/troika}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    troika_in_topic: ${TROIKA_IN_TOPIC:PRO.INTERNAL.TROIKA}
    troika_trx_in_topic: ${TROIKA_IN_TOPIC:TROIKA.TRX}
    troika_operation_out_topic: ${TROIKA_OPERATION_OUT_TOPIC:TROIKA.OPERATION.OUT}
    troika_error_out_topic: ${TROIKA_ERROR_OUT_TOPIC:TROIKA.ERROR.OUT}
    troika_batch_processing_in_group: ${BATCH_PROCESSING_IN_GROUP:troika_batch_processing_in_group}

server:
  port: 8080

service:
  troika_emission_url: ${TROIKA_EMISSION_URL:troika-emission.troika-emission.svc.cluster.local:5000}
  askp_connector_url: ${ASKP_CONNECTOR_URL:troika-askp-connector.troika-askp-connector.svc.cluster.local:5000}