spring:
  kafka:
    listener:
      ack-mode: manual
      type: batch
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: ${MAX_BATCH_SIZE:50}
      fetch-min-size: ${MIN_BATCH_SIZE:1}