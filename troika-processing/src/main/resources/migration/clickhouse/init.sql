CREATE TABLE troika_trx
(
    trx_id          UUID comment 'Идентификатор транзакции (заказа)',
    created_at      DateTime('UTC') comment 'Время формирования транзакции на терминале',
    record_at       DateTime('UTC') comment 'Время формирования транзакции на сервере',

    crd_id          UUID comment 'Идентификатор карты',
    terminal_serial String comment 'Заводской номер терминала',
    tid             String comment 'Идентификатор терминала',
    shift_num       UInt16 comment 'Номер смены на терминале',
    ern             UInt16 comment 'Единый номер операции на терминале (уникальный в рамках смены)',

    amount          UInt16 comment 'Сумма авторизации',

    card_uid        String comment 'UID носителя',
    type            Enum8('WALLET' = 0, 'TICKET' = 1) default 0 comment 'Тип билета',

    tags            Nullable(String) comment 'Тэги'
) engine = MergeTree PARTITION BY toYYYYMM(created_at)
      PRIMARY KEY (trx_id)
      SETTINGS index_granularity = 8192;

CREATE TABLE kafka_troika_trx
(
    trxId          String,
    createdAt      String,
    recordAt       String,
    crdId          String,
    terminalSerial String,
    tid            Nullable(String),
    ern            UInt64,
    shiftNum       UInt32,
    amount         UInt32,
    uid            String,
    type           Nullable(String),
    tags           Nullable(String)
)
    ENGINE = Kafka('10.4.32.25:9092,10.4.32.140:9092,10.4.32.88:9092', 'TROIKA.TRX', 'troika_trx_loader',
             'JSONEachRow') settings kafka_thread_per_consumer = 1, kafka_num_consumers = 1;

CREATE MATERIALIZED VIEW troika_trx_mv TO troika_trx as
SELECT toUUID(trxId)                             as trx_id,
       parseDateTimeBestEffort(createdAt, 'UTC') as created_at,
       parseDateTimeBestEffort(recordAt, 'UTC')  as record_at,
       toUUID(crdId)                             as crd_id,
       terminalSerial                            as terminal_serial,
       tid,
       shiftNum                                  as shift_num,
       ern,
       amount                                    as amount,
       uid                                       as card_uid,
       ifNull(type, 'WALLET')                    as type,
       tags
FROM kafka_troika_trx;

create table troika_operation
(
    trx_id    UUID comment 'Идентификатор транзакции (заказа)',
    record_at DateTime('UTC') comment 'Время формирования транзакции на сервере',
    type Enum8('RECEIVED' = 1, 'SEND' = 0) comment 'Тип операции',
    status Enum8('FAIL' = 1, 'SUCCESS' = 0) comment 'Статус операции',
    batch_id Nullable(String) comment 'Уникальный идентификатор батча',
    park Nullable(String) comment 'Код парка',
    route Nullable(String) comment 'Код маршрута',
    tid Nullable(String) comment 'Идентификатор терминала',
    ern Nullable(UInt32) comment 'ERN присвоенный ТПП',
    error_code Nullable(UInt32) comment 'Код ошибки',
    error_message Nullable(UInt32) comment 'Сообщение об ошибке'
)
    engine = MergeTree PARTITION BY toYYYYMM(record_at)
        PRIMARY KEY trx_id
        ORDER BY trx_id
        SETTINGS index_granularity = 8192;

create table kafka_troika_operation
(
    trxId    String,
    recordAt String,
    type     String,
    status   String,
    batchId Nullable(String),
    park Nullable(String),
    route Nullable(String),
    tid Nullable(String),
    ern Nullable(String),
    errorCode String,
    errorMessage Nullable(String)
)
    engine = Kafka('10.4.32.25:9092,10.4.32.140:9092,10.4.32.88:9092', 'TROIKA.OPERATION.OUT', 'troika_operation_loader',
             'JSONEachRow')
        SETTINGS kafka_thread_per_consumer = 1, kafka_num_consumers = 1;

CREATE MATERIALIZED VIEW troika_operation_mv TO troika_operation AS
SELECT toUUID(trxId)                            AS trx_id,
       parseDateTimeBestEffort(recordAt, 'UTC') AS record_at,
       type,
       status,
       batchId                                  AS batch_id,
       park,
       route,
       tid,
       toUInt32OrNull(ern)                      AS ern,
       toUInt32OrNull(errorCode)                AS error_code,
       errorMessage                             AS error_message
FROM kafka_troika_operation;
