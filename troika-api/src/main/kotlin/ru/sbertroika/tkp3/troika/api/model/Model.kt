package ru.sbertroika.tkp3.troika.api.model

import java.time.ZonedDateTime
import java.util.*

enum class TroikaTicketType {
    WALLET, TAT
}

data class TroikaTap(
    /**
     * Идентификатор транзакции
     */
    val trxId: UUID,

    /**
     * Время проведения операции
     */
    val createdAt: ZonedDateTime,

    /**
     * Идентификатор проекта
     */
    val projectId: UUID,

    /**
     * Идентификатор организации
     */
    var orgId: UUID,

    /**
     * Тип билета
     * @see TroikaTicketType
     */
    val ticketType: TroikaTicketType,

    /**
     * Порядковый номер операции на терминале (уникальный в рамках смены)
     */
    val ern: Int,

    /**
     * Внешний Идентификатор терминала
     */
    val terminalId: UUID,

    /**
     * Серийный номер терминала
     */
    val terminalSerial: String,

    /**
     * Стоимость билета
     */
    val amount: Int,

    /**
     * Номер смены на терминала
     */
    val shiftNum: Int,

    /**
     * Данные транзакции
     */
    val raw: String,

    /**
     * Внешний Идентификатор терминала
     */
    val tid: String? = null,

    /**
     * Идентификатор носителя (UID)
     */
    val cardUid: String
)