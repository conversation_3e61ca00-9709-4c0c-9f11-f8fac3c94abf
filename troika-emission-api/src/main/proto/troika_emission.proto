syntax = "proto3";

package ru.sbertroika.troika.emission.v1;

option java_multiple_files = true;
option java_package = "ru.sbertroika.troika.emission.v1";

service TroikaEmissionService {
  rpc emission(EmissionRequest) returns (EmissionResponse);
}

enum Error {
  UNKNOWN_ERROR = 0;
  SERVICE_ERROR = 1;          // Сервис временно не доступен
  UNSUPPORTED_OPERATION = 2;  // Операция не поддерживается
  BAD_REQUEST = 3;            // Неправильно сформирован запрос
}

message OperationError {
  Error error = 1;
  string message = 2;
}

message EmissionRequest {
  string number = 1;
  string uid = 2;
}

message EmissionResponse {
  oneof response {
    OperationError error  = 1;
    string cardId = 2;
  }
}