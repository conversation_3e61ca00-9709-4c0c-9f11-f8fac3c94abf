package ru.sbertroika.tkp3.troika.model

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import java.time.ZonedDateTime
import java.util.*

data class TroikaOperation(
    /**
     * Идентификатор транзакции (заказа)
     */
    @Id
    @Column("trx_id")
    var trxId: UUID? = null,

    /**
     * Время формирования записи на сервере
     */
    @Column("record_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var recordAt: ZonedDateTime? = null,

    @Column("type")
    var type: OperationType? = null,

    @Column("status")
    var status: OperationStatus? = null,

    @Column("batch_id")
    var batchId: String? = null,

    @Column("park")
    var park: String? = null,

    @Column("route")
    var route: String? = null,

    @Column("tid")
    var tid: String? = null,

    @Column("ern")
    var ern: Int? = null,

    @Column("error_code")
    var errorCode: Int? = null,

    @Column("error_message")
    var errorMessage: String? = null
)


enum class OperationType {
    /**
     * Отправлено в ТПП
     */
    SEND,

    /**
     * Получен ответ от ТПП что транзакция принята в обработку
     */
    RECEIVED
}

enum class OperationStatus {
    SUCCESS, FAIL
}