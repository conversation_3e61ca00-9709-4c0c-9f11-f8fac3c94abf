package ru.sbertroika.tkp3.troika.model

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.ZonedDateTime
import java.util.*

@Table("troika_trx")
data class TroikaTrx(

    /**
     * Идентификатор транзакции (заказа)
     */
    @Id
    @Column("trx_id")
    var trxId: UUID? = null,

    /**
     * Время формирования транзакции на терминале
     */
    @Column("created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var createdAt: ZonedDateTime? = null,

    /**
     * Время формирования транзакции на сервере
     */
    @Column("record_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var recordAt: ZonedDateTime? = null,

    /**
     * Тип билета
     */
    @Column("type")
    var ticketType: TicketType? = null,

    /**
     * Идентификатор карты
     */
    @Column("crd_id")
    var crdId: UUID? = null,

    /**
     * Заводской номер терминала
     */
    @Column("terminal_serial")
    var terminalSerial: String? = null,

    /**
     * Идентификатор терминала
     */
    @Column("tid")
    var tid: String? = null,

    /**
     * Номер смены на терминале
     */
    @Column("shift_num")
    var shiftNum: UInt? = null,

    /**
     * Единый номер операции на терминале (уникальный в рамках смены)
     */
    @Column("ern")
    var ern: UInt? = null,

    /**
     * Сумма авторизации
     */
    @Column("amount")
    var amount: UInt? = null,

    /**
     * UID носителя
     */
    @Column("card_uid")
    var uid: String? = null,

    /**
     * Тэги
     */
    @Column("tags")
    var tags: String? = null
)

enum class TicketType {
    WALLET, TICKET
}
