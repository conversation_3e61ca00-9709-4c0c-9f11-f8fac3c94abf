spring:
  application:
    name: troika-emission

  r2dbc:
    url: r2dbc:pool:${DB_URL:postgresql://postgres/troika}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://postgres/troika}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

grpc:
  port: 5000
server:
  port: 8080