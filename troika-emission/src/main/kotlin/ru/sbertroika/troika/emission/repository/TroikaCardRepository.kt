package ru.sbertroika.troika.emission.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.troika.emission.model.TroikaCard
import java.util.*

interface TroikaCardRepository : CoroutineCrudRepository<TroikaCard, UUID> {

    suspend fun findByNumber(number: String): Flow<TroikaCard>

    suspend fun findByUid(uid: String): Flow<TroikaCard>
}