package ru.sbertroika.troika.emission.grpc

import arrow.core.Either
import org.lognet.springboot.grpc.GRpcService
import ru.sbertroika.troika.emission.service.EmissionService
import ru.sbertroika.troika.emission.utils.addZeroes
import ru.sbertroika.troika.emission.utils.clearNumber
import ru.sbertroika.troika.emission.v1.*

@GRpcService
class TroikaEmissionServiceGrpc(
    private val emissionService: EmissionService
) : TroikaEmissionServiceGrpcKt.TroikaEmissionServiceCoroutineImplBase() {

    override suspend fun emission(request: EmissionRequest): EmissionResponse {
        return when (request.number != null || request.uid != null) {
            true -> {
                when (val res = emissionService.emission(request.number.clearNumber().addZeroes(), request.uid.uppercase())) {
                    is Either.Left -> EmissionResponse.newBuilder()
                        .setError(
                            OperationError.newBuilder()
                                .setError(Error.SERVICE_ERROR)
                                .build()
                        )

                    is Either.Right -> EmissionResponse.newBuilder()
                        .setCardId(res.value.toString())
                }
            }

            else -> {
                EmissionResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder()
                            .setError(Error.BAD_REQUEST)
                            .build()
                    )
            }
        }.build()
    }
}