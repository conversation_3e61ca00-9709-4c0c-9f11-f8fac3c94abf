package ru.sbertroika.troika.emission.service.impl

import arrow.core.Either
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.reactor.asFlux
import org.springframework.stereotype.Service
import ru.sbertroika.troika.emission.model.TroikaCard
import ru.sbertroika.troika.emission.repository.TroikaCardRepository
import ru.sbertroika.troika.emission.service.EmissionService
import java.util.*

@Service
class EmissionServiceImpl(
    private val troikaCardRepository: TroikaCardRepository
) : EmissionService {

    override suspend fun emission(number: String?, uid: String?): Either<Error, UUID> {
        return try {
            if (!number.isNullOrEmpty() && !uid.isNullOrEmpty()) {
                return when (val res = emissionByNumberAndUid(number, uid)) {
                    is Either.Left -> Either.Left(Error())
                    is Either.Right -> Either.Right(res.value)
                }
            }

            if (!number.isNullOrEmpty()) {
                return when (val res = emissionByNumber(number)) {
                    is Either.Left -> Either.Left(Error())
                    is Either.Right -> Either.Right(res.value)
                }
            }

            if (uid.isNullOrEmpty()) return Either.Left(Error())

            return when (val res = emissionByUid(uid)) {
                is Either.Left -> Either.Left(Error())
                is Either.Right -> Either.Right(res.value)
            }
        } catch (e: Exception) {
            Either.Left(Error())
        }
    }

    private suspend fun emissionByUid(uid: String): Either<Error, UUID> {
        return try {
            val res = troikaCardRepository.findByUid(uid)
                .asFlux()
                .awaitFirstOrNull()

            if (res == null) {
                val newCard = troikaCardRepository.save(TroikaCard(uid = uid))
                Either.Right(newCard.id!!)
            } else {
                Either.Right(res.id!!)
            }
        } catch (e: Exception) {
            Either.Left(Error())
        }
    }

    private suspend fun emissionByNumber(number: String): Either<Error, UUID> {
        return try {
            val res = troikaCardRepository.findByNumber(number)
                .asFlux()
                .awaitFirstOrNull()

            if (res == null) {
                val newCard = troikaCardRepository.save(TroikaCard(number = number))
                Either.Right(newCard.id!!)
            } else {
                Either.Right(res.id!!)
            }
        } catch (e: Exception) {
            Either.Left(Error())
        }
    }

    private suspend fun emissionByNumberAndUid(number: String, uid: String): Either<Error, UUID> {
        return try {
            val res = troikaCardRepository.findByNumber(number)
                .asFlux()
                .awaitFirstOrNull()

            if (res == null) {
                val byUid = troikaCardRepository.findByUid(uid)
                    .asFlux()
                    .awaitFirstOrNull()

                if (byUid == null) {
                    val newCard = troikaCardRepository.save(
                        TroikaCard(
                            number = number,
                            uid = uid
                        )
                    )
                    return Either.Right(newCard.id!!)
                }

                byUid.number = number
                troikaCardRepository.save(byUid)
                return Either.Right(byUid.id!!)
            } else {
                if (res.uid.isNullOrEmpty()) {
                    troikaCardRepository.save(
                        res.copy(uid=uid)
                    )
                }
            }

            Either.Right(res.id!!)
        } catch (e: Exception) {
            Either.Left(Error())
        }
    }
}