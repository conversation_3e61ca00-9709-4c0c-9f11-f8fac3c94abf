package ru.sbertroika.troika.emission.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("card")
data class TroikaCard(
    @Id
    @Column("id")
    var id: UUID? = null,

    @Column("created_at")
    var createdAt: Timestamp? = null,

    @Column("card_number")
    var number: String? = null,

    @Column("uid")
    var uid: String? = null
)
