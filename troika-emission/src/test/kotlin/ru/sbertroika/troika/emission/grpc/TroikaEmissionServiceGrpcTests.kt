package ru.sbertroika.troika.emission.grpc

import io.grpc.ManagedChannelBuilder
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import ru.sbertroika.troika.emission.utils.addZeroes
import ru.sbertroika.troika.emission.utils.clearNumber
import ru.sbertroika.troika.emission.v1.EmissionRequest
import ru.sbertroika.troika.emission.v1.TroikaEmissionServiceGrpcKt
import java.util.concurrent.TimeUnit

class TroikaEmissionServiceGrpcTests {

    companion object {
        private val server = "localhost:5007"
    }

    @Test
    fun testClearNumber() {
        Assertions.assertEquals("0019030603", "0019 030 603".clearNumber())
        Assertions.assertEquals("0019030603", "0019-030-603".clearNumber())
    }

    @Test
    fun testAddZeroes() {
        Assertions.assertEquals("0019030603", "19 030 603".clearNumber().addZeroes())
        Assertions.assertEquals("0000000001", "1".addZeroes())
        Assertions.assertEquals("", "".addZeroes())
    }

    @Test
    fun emission(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = TroikaEmissionServiceGrpcKt.TroikaEmissionServiceCoroutineStub(channel)
            val request = EmissionRequest.newBuilder()
                .setNumber("0019030604")
                .setUid("04984DAA0B5381")
                .build()
            val response = client.emission(request)
            println("response: $response")
            assertFalse(response.hasError())
            Assertions.assertNotNull(response.cardId)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun emissionInvalidNumber(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = TroikaEmissionServiceGrpcKt.TroikaEmissionServiceCoroutineStub(channel)
            val request = EmissionRequest.newBuilder()
                .setNumber("0019 030-603")
                .setUid("04984DAA0B5380")
                .build()
            val response = client.emission(request)
            println("response: $response")
            assertFalse(response.hasError())
            Assertions.assertNotNull(response.cardId)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }
}