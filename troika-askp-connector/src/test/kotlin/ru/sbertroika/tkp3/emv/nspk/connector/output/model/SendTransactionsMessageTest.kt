package ru.sbertroika.tkp3.emv.nspk.connector.output.model

import org.junit.jupiter.api.Test
import ru.sbertroika.tkp3.emv.nspk.connector.decodeHex
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

class SendTransactionsMessageTest {

    companion object {
        const val terminalId = "11"
        const val sessionId = "9ff31f90-f979-459a-8a00-4ace8ec4cb1c"
        const val trxId = "3f35b588-fcad-4e04-b0b6-78a0c0049ce2"
        private val fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss ZZZZZ")
        private val date = ZonedDateTime.parse("2024-01-05 10:50:01 +03:00", fmt)
        private val trxTime = ZonedDateTime.parse("2024-01-04 14:20:24 +03:00", fmt)
    }

    @Test
    fun test() {
        println(ZonedDateTime.now().format(fmt))
    }

    @Test
    fun parseMessage() {
        val message = "<?xml version=\"1.0\" encoding=\"WINDOWS-1251\"?>\n" +
                "<message>\n" +
                "  <head>\n" +
                "    <api_version>1.0</api_version>\n" +
                "    <code>0</code>\n" +
                "    <comment></comment>\n" +
                "    <time>2024-01-05 12:34:50 +03:00</time>\n" +
                "    <session_id>13204573565435463582</session_id>\n" +
                "  </head>\n" +
                "  <body/>\n" +
                "</message>"
        println(message.toSendTransactionsResponse())
    }

    @Test
    fun makeMessageTest() {
        val message = SendTransactionsMessage(
            head = Head(
                apiVersion = "1.0",
                msgType = "send_transactions",
                devType = "VALIDT",
                devSerial = terminalId,
                time = date.format(fmt),
                sessionId = UUID.fromString(sessionId).mostSignificantBits.toULong().toString()
            ),
            body = Batch(
                messages = ArrayList(
                    listOf(
                        AskpMessage(
                            type = 50,
                            trxNo = UUID.fromString(trxId).mostSignificantBits.toULong().toString(),
                            routeNo = 7399,
                            driverNo = 0,
                            shiftNo = 1,
                            park = 899,
                            trxTime = trxTime.format(fmt),
                            attributes = Attributes(
                                serial = Base64.getEncoder().encodeToString("042C6EEAD25E80000000".decodeHex()),
                                data = Base64.getEncoder().encodeToString("45DB1ED4C94F2E2B891484EE5D20F8004EEC400000164400ACC812D69CDDDB8E4EEC400000164400ACC812D69CDDDB8E".decodeHex()),
                                cost = "3000"
                            )
                        )
                    )
                )
            )
        )
        println(message.toXml())
    }
}