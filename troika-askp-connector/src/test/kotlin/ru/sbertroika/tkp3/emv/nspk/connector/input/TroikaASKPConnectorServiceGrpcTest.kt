package ru.sbertroika.tkp3.emv.nspk.connector.input

import com.google.protobuf.Timestamp
import io.grpc.*
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import ru.sbertroika.troika.askp.connector.PassType
import ru.sbertroika.troika.askp.connector.TroikaASKPConnectorGrpcKt
import ru.sbertroika.troika.askp.connector.batchRequest
import ru.sbertroika.troika.askp.connector.pass

@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class TroikaASKPConnectorServiceGrpcTest {

    companion object {
        private const val server = "localhost"
        private const val port = 5005
        private const val isTls = false
    }

    @Test
    fun sendBatchTest(): Unit = runBlocking {
        val response = client().sendBatch(
            batchRequest {
                this.terminalId = "123"
                this.pass += listOf(
                    pass {
                        type = PassType.PT_WALLET
                        trxId = "3f35b588-fcad-4e04-b0b6-78a0c0049ce2"
                        createdAt = Timestamp.getDefaultInstance()
                        routeNumber = 7399
                        driverNumber = 0
                        shiftNumber = 1
                        park = 899
                        uid = "042C6EEAD25E80000000"
                        data = "45DB1ED4C94F2E2B891484EE5D20F8004EEC400000164400ACC812D69CDDDB8E4EEC400000164400ACC812D69CDDDB8E"
                        amount = 200
                    }
                )
            }
        )
        println("response: $response")
        assertFalse(response.hasError())
        assertNotNull(response.sessionId)
    }

    private fun client(): TroikaASKPConnectorGrpcKt.TroikaASKPConnectorCoroutineStub {
        return if (isTls) {
            val credentials: ChannelCredentials = TlsChannelCredentials.newBuilder() //You can use your own certificate here .trustManager(new File("cert.pem"))
                .trustManager(InsecureTrustManagerFactory.INSTANCE.trustManagers[0])
                .build()
            val channel: ManagedChannel = Grpc.newChannelBuilderForAddress(server, port, credentials)
                .build()
            return TroikaASKPConnectorGrpcKt.TroikaASKPConnectorCoroutineStub(channel)
        } else {
            val channel = ManagedChannelBuilder.forTarget("$server:$port")
                .usePlaintext()
                .build()
            TroikaASKPConnectorGrpcKt.TroikaASKPConnectorCoroutineStub(channel)
        }
    }
}