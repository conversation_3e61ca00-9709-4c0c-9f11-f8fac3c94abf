package logging;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

@Slf4j
@UtilityClass
public class LogUtil {

    static final String HIDDEN_STRING = "***HIDDEN***";

    private static final Pattern PATTERN_FOR_HEADERS = Pattern.compile(
            "^((([a-zA-Z_0-9-]*)credit([a-zA-Z_0-9-]*))|(([a-zA-Z_0-9-]*)(secret|token))|(credit|passwd|pwd|sign|set-cookie|authorization): )(.*)$",
            Pattern.CASE_INSENSITIVE
            );

    private static final Pattern PATTERN_FOR_PARAMS = Pattern.compile(
            "((^|&)((([a-zA-Z_0-9-]*)credit([a-zA-Z_0-9-]*))|(([a-zA-Z_0-9-]*)(secret|key|token))|(credit|passwd|pwd|sign|set-cookie|authorization))=)([^&]*)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern PATTERN_FOR_BODY = Pattern.compile(
            "(\"((([a-zA-Z_0-9-]*)credit([a-zA-Z_0-9-]*))|(([a-zA-Z_0-9-]*)(password|secret|key|userName))|(credit|passwd|pwd|sign|set-cookie|authorization))\":\")((?:\\\\\"|[^\"]){0,1000})",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern PATTERN_FOR_BODY_TOKEN = Pattern.compile(
            "(\"(([a-zA-Z_0-9-]*)token)\":\")([^\"]{0,10000})",
            Pattern.CASE_INSENSITIVE
    );

    public static String sanitizeLog(String str) {
        try {
            if (str.startsWith("{") && str.endsWith("}")) {
                return sanitizeBody(str);
            } else {
                return sanitizeParams(sanitizeHeaders(str));
            }
        } catch (Throwable e) {
            log.error("Cannot sanitize logs", e);
            return str;
        }
    }

    private String sanitizeHeaders(String str) {
        return PATTERN_FOR_HEADERS.matcher(str).replaceAll("$1" + HIDDEN_STRING);
    }

    private String sanitizeParams(String str) {
        return PATTERN_FOR_PARAMS.matcher(str).replaceAll("$1" + HIDDEN_STRING);
    }

    private String sanitizeBody(String str) {
        String result = PATTERN_FOR_BODY.matcher(str).replaceAll("$1" + HIDDEN_STRING);
        return PATTERN_FOR_BODY_TOKEN.matcher(result).replaceAll("$1" + HIDDEN_STRING);
    }
}
