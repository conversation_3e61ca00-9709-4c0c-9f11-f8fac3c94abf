package logging;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;

import java.io.IOException;

@Slf4j
public class LoggingInterceptor implements Interceptor {

    @Override
    public Response intercept(Chain chain) throws IOException {
        Helper helper = new Helper();
        try {
            return helper.intercept(chain);
        } finally {
            log.info("RESPONSE: {}", helper.getLogString());
        }
    }

    private static class Helper implements Interceptor {

        private final HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor(this::log);

        private final StringBuilder logBuilder = new StringBuilder();

        @Override
        public Response intercept(Chain chain) throws IOException {
            String path = chain.request().url().url().getPath();

            // TODO рефактор может из конфига подтягивать или сделать статик лист
            if (path.contains("Blank")) {
                httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.NONE);
            } else {
                httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
            }
            return httpLoggingInterceptor.intercept(chain);
        }

        public String getLogString() {
            return logBuilder.toString();
        }

        private void log(String value) {
            if (logBuilder.length() != 0)
                logBuilder.append("\n");

            logBuilder.append(LogUtil.sanitizeLog(value));

            if (value.startsWith("--> END")) {
                log.info("REQUEST: {}", getLogString());
                logBuilder.setLength(0);
            }
        }
    }
}
