package ru.sbertroika.tkp3.emv.nspk.connector.input

import org.lognet.springboot.grpc.GRpcService
import ru.sbertroika.common.toOperationError
import ru.sbertroika.common.toZonedDateTime
import ru.sbertroika.common.v1.operationError
import ru.sbertroika.tkp3.emv.nspk.connector.model.AskpError
import ru.sbertroika.tkp3.emv.nspk.connector.model.Batch
import ru.sbertroika.tkp3.emv.nspk.connector.output.ASKPSenderService
import ru.sbertroika.troika.askp.connector.*

@GRpcService
class TroikaASKPConnectorServiceGrpc(
    private val askpSenderService: ASKPSenderService
) : TroikaASKPConnectorGrpcKt.TroikaASKPConnectorCoroutineImplBase() {

    override suspend fun sendBatch(request: BatchRequest): BatchResponse {
        return askpSenderService.sendBatch(
            Batch(
                terminalId = request.terminalId,
                passes = request.passList.map(this::toPass).toList()
            )
        ).fold(
            { err ->
                if (err is AskpError) {
                    batchResponse {
                        error = operationError {
                            code = err.code
                            message = err.comment
                        }
                    }
                } else {
                    batchResponse {
                        error = toOperationError(Error(err))
                    }
                }
            },
            { res ->
                batchResponse {
                    sessionId = res
                }
            }
        )
    }

    fun toPass(data: Pass): ru.sbertroika.tkp3.emv.nspk.connector.model.Pass = ru.sbertroika.tkp3.emv.nspk.connector.model.Pass(
        trxId = data.trxId,
        type = data.type,
        createdAt = data.createdAt.toZonedDateTime(),
        routeNumber = data.routeNumber,
        driverNumber = data.driverNumber,
        shiftNumber = data.shiftNumber,
        park = data.park,
        amount = data.amount,
        uid = data.uid,
        data = data.data
    )
}