package ru.sbertroika.tkp3.emv.nspk.connector.output

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.gildor.coroutines.okhttp.await
import ru.sbertroika.common.BadRequest
import ru.sbertroika.common.ServiceError
import ru.sbertroika.tkp3.emv.nspk.connector.output.model.*
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*


@Service
class ASKPClient(
    private val client: OkHttpClient,
    @Value("\${client.unsafe}")
    private var unsafeClient: Boolean = false,
    @Value("\${client.askp.url}")
    private val askpUrl: String
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    suspend fun sendBatch(terminalId: String, sessionId: String,  batch: Batch): Either<Error, SendTransactionsResponse> {
        return sendMessage(
            url = askpUrl,
            message = SendTransactionsMessage(
                head = Head(
                    apiVersion = "1.0",
                    msgType = "send_transactions",
                    devType = "VALIDT",
                    devSerial = terminalId,
                    time = ZonedDateTime.now(ZoneId.of("UTC")).format(fmt),
                    sessionId = UUID.fromString(sessionId).mostSignificantBits.toULong().toString()
                ),
                body = batch
            )
        ).fold(
            {
                log.error("Error sendBatch", it)
                it.left()
            },
            {
                it.right()
            }
        )
    }

    private suspend fun sendMessage(url: String, message: SendTransactionsMessage): Either<Error, SendTransactionsResponse> {
        val request = Request.Builder()
            .url(url)
            .addHeader("Content-Type", "application/xml; charset=utf-8")
            .post(message.toXml().toRequestBody("application/soap+xml;charset=UTF-8".toMediaType()))
            .build()

        try {
            val response = client.newCall(request).await()
            if (response.isSuccessful) {
                return response.body!!.string().toSendTransactionsResponse().right()
            }

            return when (response.code) {
                400 -> BadRequest(response.body?.string()).left()
                else -> ServiceError(response.body?.string()).left()
            }
        } catch (e: Exception) {
            return Error(e).left()
        }
    }

    companion object {
        private val fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss ZZZZZ")
    }
}