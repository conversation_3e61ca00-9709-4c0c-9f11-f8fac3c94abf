package ru.sbertroika.tkp3.emv.nspk.connector.model

import ru.sbertroika.troika.askp.connector.PassType
import java.time.ZonedDateTime

data class Pass(
    val trxId: String,
    val type: PassType,
    val createdAt: ZonedDateTime,
    val routeNumber: Int,
    val driverNumber: Int,
    val shiftNumber: Int,
    val park: Int,
    val amount: Int,
    val uid: String,
    val data: String
)

data class Batch(
    val terminalId: String,
    val passes: List<Pass>
)

data class AskpError(
    val code: Int,
    val comment: String
) : Error()