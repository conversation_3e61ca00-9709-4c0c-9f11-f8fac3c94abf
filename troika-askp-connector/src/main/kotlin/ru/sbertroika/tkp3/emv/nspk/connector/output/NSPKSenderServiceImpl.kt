package ru.sbertroika.tkp3.emv.nspk.connector.output

import arrow.core.Either
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.emv.nspk.connector.decodeHex
import ru.sbertroika.tkp3.emv.nspk.connector.model.AskpError
import ru.sbertroika.tkp3.emv.nspk.connector.model.Batch
import ru.sbertroika.tkp3.emv.nspk.connector.output.model.AskpMessage
import ru.sbertroika.tkp3.emv.nspk.connector.output.model.Attributes
import ru.sbertroika.troika.askp.connector.PassType
import java.time.format.DateTimeFormatter
import java.util.*

@Service
class NSPKSenderServiceImpl(
    private val client: ASKPClient,
) : ASKPSenderService {

    override suspend fun sendBatch(batch: Batch): Either<Throwable, String> = Either.catch {
        val sessionId = UUID.randomUUID().toString()
        client.sendBatch(
            terminalId = batch.terminalId,
            sessionId = sessionId,
            batch = ru.sbertroika.tkp3.emv.nspk.connector.output.model.Batch(
                messages = ArrayList(batch.passes.map { pass ->
                    AskpMessage(
                        type = when (pass.type) {
                            PassType.PT_WALLET -> 50
                            PassType.PT_TAT -> 56
                            else -> 50
                        },
                        trxNo = UUID.fromString(pass.trxId).mostSignificantBits.toULong().toString(),
                        routeNo = pass.routeNumber,
                        driverNo = pass.driverNumber,
                        shiftNo = pass.shiftNumber,
                        park = pass.park,
                        trxTime = pass.createdAt.format(fmt),
                        attributes = Attributes(
                            serial = Base64.getEncoder().encodeToString(pass.uid.decodeHex()),
                            data = Base64.getEncoder().encodeToString(pass.data.decodeHex()),
                            cost = "${pass.amount}"
                        )
                    )
                }.toMutableList())
            )
        ).fold(
            {
                throw it
            },
            { res ->
                if (res.head!!.code == 0) {
                    sessionId
                } else {
                    throw AskpError(
                        code = res.head!!.code!!,
                        comment = res.head!!.comment!!
                    )
                }
            }
        )
    }

    companion object {
        private val fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss ZZZZZ")
    }
}