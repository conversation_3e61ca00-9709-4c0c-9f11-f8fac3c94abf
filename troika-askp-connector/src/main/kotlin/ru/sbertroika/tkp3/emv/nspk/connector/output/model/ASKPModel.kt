package ru.sbertroika.tkp3.emv.nspk.connector.output.model

import org.simpleframework.xml.Default
import org.simpleframework.xml.Element
import org.simpleframework.xml.ElementList
import org.simpleframework.xml.Root
import org.simpleframework.xml.core.Persister
import java.io.StringWriter

@Default
@Root(name = "message", strict = false)
data class SendTransactionsMessage(
    val head: Head,
    val body: Batch
) {
    fun toXml(): String {
        val serializer = Persister()
        val writer = StringWriter()
        serializer.write(this, writer)
        return writer.toString()
    }
}

@Default
@Root(strict = false, name = "message")
data class SendTransactionsResponse(
    var head: Head? = null
)

fun String.toMessage(): SendTransactionsMessage {
    val serializer = Persister()
    return serializer.read(SendTransactionsMessage::class.java, this)
}

fun String.toSendTransactionsResponse(): SendTransactionsResponse {
    val serializer = Persister()
    return serializer.read(SendTransactionsResponse::class.java, this.replace("<body/>", ""), false)
}

data class Head(
    @field:Element(name = "api_version", required = false)
    var apiVersion: String? = null,

    @field:Element(name = "msg_type", required = false)
    var msgType: String? = null,

    @field:Element(name = "dev_type", required = false)
    var devType: String? = null,

    @field:Element(name = "dev_serial", required = false)
    var devSerial: String? = null,

    @field:Element(name = "time", required = false)
    var time: String? = null,

    @field:Element(name = "session_id", required = false)
    var sessionId: String? = null,

    @field:Element(name = "code", required = false)
    var code: Int? = null,

    @field:Element(name = "comment", required = false)
    var comment: String? = null
)

@Root(name = "transactions", strict = false)
data class Batch(
    @field:ElementList(name = "transactions", entry = "transaction", required = false)
    val messages: ArrayList<AskpMessage>
)

data class AskpMessage(

    /**
     * Тип транзакции
     * Допустимые в текущей версии типы:
     * 40 – проход по БСК
     * 41 – аннулирование по стоп-листу БСК
     * 50 – проход
     * 51 – аннулирование по стоп-листу ЭК
     * 103 – открытие смены
     * 104 – закрытие смены
     */
    @field:Element(name = "type", required = false)
    val type: Int,

    /**
     * Номер транзакции
     */
    @field:Element(name = "trx_no", required = false)
    val trxNo: String,

    /**
     * Код маршрута
     */
    @field:Element(name = "route_no", required = false)
    val routeNo: Int,

    /**
     * Номер водителя
     */
    @field:Element(name = "driver_no", required = false)
    val driverNo: Int,

    /**
     * Номер смены на момент формирования заголовка транзакции
     */
    @field:Element(name = "shift_no", required = false)
    val shiftNo: Int,

    /**
     * Код парка
     */
    @field:Element(name = "park", required = false)
    val park: Int,

    /**
     * Время совершения транзакции
     */
    @field:Element(name = "trx_time", required = false)
    val trxTime: String,

    /**
     * Код парка
     */
    @field:Element(name = "attributes", required = true)
    val attributes: Attributes,
)

@Root(name = "attributes", strict = false)
data class Attributes(

    @field:Element(name = "serial", required = true)
    val serial: String,

    @field:Element(name = "data", required = true)
    val data: String,

    @field:Element(name = "cost", required = true)
    val cost: String,
)