package ru.sbertroika.tkp3.emv.nspk.connector.config

import logging.LoggingInterceptor
import okhttp3.OkHttpClient
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.io.File
import java.security.KeyStore
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManagerFactory
import javax.net.ssl.X509TrustManager


@Configuration
class ClientConfig {

    @Value("\${client.logging.enabled}")
    private var loggingEnabled: Boolean = true

    @Value("\${client.unsafe}")
    private var unsafeClient: Boolean = true

    @Value("\${client.timeout}")
    private var timeout: Long = 60L

    @Value("\${client.keystore.path}")
    private val keystorePath: String? = null

    @Value("\${client.keystore.password}")
    private val keystorePass: String? = null

    @Value("\${client.keystore.key.alias}")
    private val keyAlias: String? = null

    @Value("\${client.keystore.key.password}")
    private val keyPassword: String? = null

    @Bean
    fun client(): OkHttpClient = try {
        if (unsafeClient) {
            val builder = OkHttpClient.Builder()
                .connectTimeout(timeout, TimeUnit.SECONDS)
                .writeTimeout(timeout, TimeUnit.SECONDS)
                .readTimeout(timeout, TimeUnit.SECONDS)
                .callTimeout(timeout, TimeUnit.SECONDS)
                .hostnameVerifier { _, _ -> true }
            if (loggingEnabled) {
                builder.addInterceptor(LoggingInterceptor())
            }
            builder.build()
        } else {

            val keystore = KeyStore.getInstance("PKCS12")
            File(keystorePath!!).inputStream().use {
                keystore.load(it, keystorePass!!.toCharArray())
            }

            val kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm())
            kmf.init(keystore, keyPassword!!.toCharArray())

            // creating a TrustManager that trusts the CAs in our KeyStore
            val tmfAlgorithm = TrustManagerFactory.getDefaultAlgorithm()
            val tmf = TrustManagerFactory.getInstance(tmfAlgorithm)
            tmf.init(keystore)

            val trustAllCerts = arrayOf<X509TrustManager>(
                object : X509TrustManager {
                    override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
                    override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {}
                    override fun getAcceptedIssuers(): Array<X509Certificate> {
                        return arrayOf()
                    }
                }
            )

            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(kmf.keyManagers, trustAllCerts, null)

            val builder = OkHttpClient.Builder()
                .sslSocketFactory(sslContext.socketFactory, trustAllCerts[0])
                .connectTimeout(timeout, TimeUnit.SECONDS)
                .writeTimeout(timeout, TimeUnit.SECONDS)
                .readTimeout(timeout, TimeUnit.SECONDS)
                .callTimeout(timeout, TimeUnit.SECONDS)
                .hostnameVerifier { _, _ -> true }
            if (loggingEnabled) {
                builder.addInterceptor(LoggingInterceptor())
            }
            builder.build()
        }
    } catch (e: Exception) {
        throw RuntimeException(e)
    }
}
