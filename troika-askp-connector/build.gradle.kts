import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import com.google.protobuf.gradle.*

plugins {
    idea
    id("org.springframework.boot") version "3.0.1"
    id("io.spring.dependency-management") version "1.1.0"
    kotlin("jvm")
    kotlin("plugin.spring")
    id("com.google.protobuf")
}

group = "ru.sbertroika.cbt"
version = rootProject.version
java.sourceCompatibility = JavaVersion.VERSION_17

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    implementation("ru.sbertroika.common:common-api:1.0.6")
    implementation(project(":troika-askp-connector-api"))

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("io.github.lognet:grpc-spring-boot-starter:5.0.0")
    implementation("org.springframework.boot:spring-boot-starter-webflux")

    //Kotlin
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib)
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.14.0")

    //Kotlin-ext
    implementation("io.arrow-kt:arrow-core:1.0.1")

    //Lombok
    compileOnly("org.projectlombok:lombok")
    developmentOnly("org.springframework.boot:spring-boot-devtools")
    annotationProcessor("org.projectlombok:lombok")

    //gRPC
    implementation(libs.grpcKotlinStub)
    implementation(libs.grpcProtobuf)
    implementation(libs.protobufKotlin)
    implementation(libs.grpcStub)
    implementation("io.grpc:grpc-netty:1.43.0")
    implementation("io.grpc:grpc-okhttp:1.43.0")

    //okHttp
    implementation("com.squareup.okhttp3:okhttp:4.10.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.10.0")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.14.0")
    implementation("ru.gildor.coroutines:kotlin-coroutines-okhttp:1.0")
//    implementation "com.fasterxml.jackson.dataformat:jackson-dataformat-xml:$jacksonModuleKotlinVersion"

    implementation("org.simpleframework:simple-xml:2.7.1")

    //Test
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testCompileOnly("org.projectlombok:lombok")
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        // Exclude the test engine you don't need
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
    }
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
    testLogging.showStandardStreams = true
}

tasks.named<Test>("test") {
    useJUnitPlatform()
}

tasks.named<Jar>("jar") {
    enabled = false
}
