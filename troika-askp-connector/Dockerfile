FROM gradle:8.13-jdk17-alpine as builder
ARG GRADLE_USER_HOME=/tmp/.gradle
ENV GRADLE_USER_HOME=$GRADLE_USER_HOME
RUN apk add gcompat
WORKDIR /build
ADD . /build
COPY .ci-gradle/gradle.properties /tmp/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /home/<USER>/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /root/.gradle/gradle.properties


RUN env

RUN gradle --no-daemon :troika-askp-connector:bootJar -i

FROM openjdk:17-alpine
# Если нужны нативные библиотеки, раскомментируйте следующую строку
# RUN apk add --no-cache libstdc++
# COPY ./lib-tms/lib/* /opt/libs/
# ENV LD_LIBRARY_PATH=/opt/libs

COPY --from=builder /build/troika-askp-connector/build/libs/troika-askp-connector-*.jar ./troika-askp-connector.jar

EXPOSE 8080
EXPOSE 6000

ENTRYPOINT ["java", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-Djava.security.egd=file:/dev/./urandom", "-jar", "troika-askp-connector.jar"]