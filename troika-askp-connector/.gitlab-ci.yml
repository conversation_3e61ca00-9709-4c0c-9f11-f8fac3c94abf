include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# TROIKA-ASKP-CONNECTOR: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
troika_askp_connector_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "troika-askp-connector"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_gradle_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - troika-askp-connector/**
        - ./*

troika_askp_connector_helm_kubeval_testing_develop:
  stage: test
  needs: [troika_askp_connector_build_develop]
  variables:
    SERVICE_NAME: "troika-askp-connector"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - troika-askp-connector/**
        - charts/troika-askp-connector/**

troika_askp_connector_deploy_chart_develop:
  stage: deploy
  needs:
    - troika_askp_connector_helm_kubeval_testing_develop
    - job: troika_askp_connector_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "troika-askp-connector"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - troika-askp-connector/**
        - charts/troika-askp-connector/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - troika-askp-connector/**

troika_askp_connector_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "troika-askp-connector"
  extends:
    - .docker_gradle_build_and_push
  <<: *tag_rules

troika_askp_connector_helm_kubeval_testing_tag:
  stage: test
  needs:
    - troika_askp_connector_build_tag
  variables:
    SERVICE_NAME: "troika-askp-connector"
  extends:
    - .validate_helm_template
  <<: *tag_rules

troika_askp_connector_deploy_chart_tag:
  stage: deploy
  needs:
    - troika_askp_connector_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "troika-askp-connector"
  extends:
    - .deploy_helm_template
  <<: *tag_rules
